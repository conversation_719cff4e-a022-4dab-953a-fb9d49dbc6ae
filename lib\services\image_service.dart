import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';

class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  // Process captured image for OCR
  Future<File> processImageForOcr(String imagePath) async {
    try {
      final originalFile = File(imagePath);
      final bytes = await originalFile.readAsBytes();

      // Decode the image
      img.Image? image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Process the image for better OCR results
      image = _enhanceImageForOcr(image);

      // Save the processed image
      final processedFile = await _saveProcessedImage(image, imagePath);

      return processedFile;
    } catch (e) {
      throw Exception('Failed to process image: $e');
    }
  }

  // Enhance image for better OCR results
  img.Image _enhanceImageForOcr(img.Image image) {
    // Resize if too large (maintain aspect ratio)
    if (image.width > AppConstants.maxImageWidth ||
        image.height > AppConstants.maxImageHeight) {
      final aspectRatio = image.width / image.height;
      int newWidth, newHeight;

      if (aspectRatio > 1) {
        // Landscape
        newWidth = AppConstants.maxImageWidth.toInt();
        newHeight = (newWidth / aspectRatio).toInt();
      } else {
        // Portrait
        newHeight = AppConstants.maxImageHeight.toInt();
        newWidth = (newHeight * aspectRatio).toInt();
      }

      image = img.copyResize(image, width: newWidth, height: newHeight);
    }

    // Enhance contrast and brightness for better text recognition
    image = img.adjustColor(
      image,
      contrast: 1.2,
      brightness: 1.1,
      saturation: 0.9,
    );

    // Apply slight sharpening
    image = img.convolution(image, filter: [0, -1, 0, -1, 5, -1, 0, -1, 0]);

    return image;
  }

  // Save processed image
  Future<File> _saveProcessedImage(img.Image image, String originalPath) async {
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'processed_$timestamp.jpg';
    final filePath = path.join(directory.path, 'images', fileName);

    // Create directory if it doesn't exist
    final imageDir = Directory(path.dirname(filePath));
    if (!await imageDir.exists()) {
      await imageDir.create(recursive: true);
    }

    // Encode and save
    final jpegBytes = img.encodeJpg(image, quality: AppConstants.imageQuality);
    final file = File(filePath);
    await file.writeAsBytes(jpegBytes);

    return file;
  }

  // Crop image to focus area
  Future<File> cropImageToFocusArea(String imagePath, Rect cropRect) async {
    try {
      final originalFile = File(imagePath);
      final bytes = await originalFile.readAsBytes();

      img.Image? image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Convert relative crop rect to absolute pixels
      final cropX = (cropRect.left * image.width).toInt();
      final cropY = (cropRect.top * image.height).toInt();
      final cropWidth = (cropRect.width * image.width).toInt();
      final cropHeight = (cropRect.height * image.height).toInt();

      // Crop the image
      final croppedImage = img.copyCrop(
        image,
        x: cropX,
        y: cropY,
        width: cropWidth,
        height: cropHeight,
      );

      // Save cropped image
      final croppedFile = await _saveProcessedImage(croppedImage, imagePath);

      return croppedFile;
    } catch (e) {
      throw Exception('Failed to crop image: $e');
    }
  }

  // Rotate image
  Future<File> rotateImage(String imagePath, int degrees) async {
    try {
      final originalFile = File(imagePath);
      final bytes = await originalFile.readAsBytes();

      img.Image? image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Rotate the image
      final rotatedImage = img.copyRotate(image, angle: degrees.toDouble());

      // Save rotated image
      final rotatedFile = await _saveProcessedImage(rotatedImage, imagePath);

      return rotatedFile;
    } catch (e) {
      throw Exception('Failed to rotate image: $e');
    }
  }

  // Get image info
  Future<ImageInfo> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      final fileSize = await file.length();

      img.Image? image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      return ImageInfo(
        width: image.width,
        height: image.height,
        fileSize: fileSize,
        format: path.extension(imagePath).toLowerCase(),
        path: imagePath,
      );
    } catch (e) {
      throw Exception('Failed to get image info: $e');
    }
  }

  // Validate image for OCR processing
  Future<ImageValidationResult> validateImageForOcr(String imagePath) async {
    try {
      final info = await getImageInfo(imagePath);
      final issues = <String>[];

      // Check file size (should not be too large or too small)
      if (info.fileSize > 10 * 1024 * 1024) {
        // 10MB
        issues.add(
          'Image file is too large (${AppUtils.formatFileSize(info.fileSize)})',
        );
      } else if (info.fileSize < 50 * 1024) {
        // 50KB
        issues.add(
          'Image file is too small (${AppUtils.formatFileSize(info.fileSize)})',
        );
      }

      // Check dimensions
      if (info.width < 300 || info.height < 300) {
        issues.add(
          'Image resolution is too low (${info.width}x${info.height})',
        );
      }

      // Check aspect ratio (should be reasonable)
      final aspectRatio = info.width / info.height;
      if (aspectRatio > 3 || aspectRatio < 0.33) {
        issues.add(
          'Image aspect ratio is unusual (${aspectRatio.toStringAsFixed(2)})',
        );
      }

      // Check format
      if (!['jpg', 'jpeg', 'png'].contains(info.format.replaceAll('.', ''))) {
        issues.add('Unsupported image format (${info.format})');
      }

      return ImageValidationResult(
        isValid: issues.isEmpty,
        issues: issues,
        imageInfo: info,
      );
    } catch (e) {
      return ImageValidationResult(
        isValid: false,
        issues: ['Failed to validate image: $e'],
        imageInfo: null,
      );
    }
  }

  // Clean up temporary images
  Future<void> cleanupTempImages() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imageDir = Directory(path.join(directory.path, 'images'));

      if (await imageDir.exists()) {
        final files = await imageDir.list().toList();
        final now = DateTime.now();

        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);

            // Delete files older than 7 days
            if (age.inDays > 7) {
              await file.delete();
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Failed to cleanup temp images: $e');
    }
  }

  // Get storage usage
  Future<int> getStorageUsage() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imageDir = Directory(path.join(directory.path, 'images'));

      if (!await imageDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = await imageDir.list(recursive: true).toList();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('Failed to get storage usage: $e');
      return 0;
    }
  }
}

// Image information class
class ImageInfo {
  final int width;
  final int height;
  final int fileSize;
  final String format;
  final String path;

  ImageInfo({
    required this.width,
    required this.height,
    required this.fileSize,
    required this.format,
    required this.path,
  });

  double get aspectRatio => width / height;
  String get resolution => '${width}x$height';
  String get fileSizeFormatted => AppUtils.formatFileSize(fileSize);
}

// Image validation result
class ImageValidationResult {
  final bool isValid;
  final List<String> issues;
  final ImageInfo? imageInfo;

  ImageValidationResult({
    required this.isValid,
    required this.issues,
    this.imageInfo,
  });
}
